package com.seer.trick.fleet.service

import com.seer.trick.fleet.domain.MapPath
import com.seer.trick.fleet.domain.SceneContainerType
import com.seer.trick.fleet.domain.normalizeRadian
import com.seer.trick.fleet.map.AreaMapCache
import com.seer.trick.fleet.traffic.venus.State
import com.seer.trick.fleet.traffic.venus.cache.DijkstraCache
import org.slf4j.LoggerFactory
import kotlin.math.PI
import kotlin.math.abs

  /**
   * 窄边通行
   * 负责处理机器人在窄边通行模式下的路径规划和容器旋转逻辑
   */
class NarrowSideAheadService(
  private val sr: SceneRuntime
) {

  private val logger = LoggerFactory.getLogger(javaClass)

  // 缓存容器类型信息，避免重复查询
  private val containerTypeCache = mutableMapOf<String, SceneContainerType>()

  // 角度容差，5度
  private val angleTolerance = Math.toRadians(5.0)
  /**
   * 检查是否需要进行窄边通行处理
   */
  fun shouldPerformNarrowSideAhead(robotName: String, path: MapPath, fromState: State): Boolean {
    return try {
      checkNarrowSideAheadConditions(robotName, path, fromState)
    } catch (e: Exception) {
      logger.warn("check Narrow Side Ahead Conditions failed: robotName=$robotName, error=${e.message}")
      false
    }
  }

  /**
   * 寻找最佳换向点
   */
  fun findBestRotationPoint(robotName: String, fromPointName: String, toPointName: String): String? {
    return try {
      findOptimalRotationPoint(robotName, fromPointName, toPointName)
    } catch (e: Exception) {
      logger.warn("find Best Rotation Point failed: robotName=$robotName, from=$fromPointName, to=$toPointName, error=${e.message}")
      null
    }
  }

  /**
   * 调整货物朝向以适应窄边通行
   */
  fun adjustLoadThetaForNarrowSideAhead(
    robotName: String,
    originalLoadEnterTheta: Double,
    originalLoadExitTheta: Double,
    pathEnterTheta: Double,
    pathExitTheta: Double
  ): Pair<Double, Double> {
    return try {
      calculateOptimalLoadThetas(robotName, originalLoadEnterTheta, originalLoadExitTheta, pathEnterTheta, pathExitTheta)
    } catch (e: Exception) {
      logger.warn("adjust Load Theta failed: robotName=$robotName, error=${e.message}")
      originalLoadEnterTheta to originalLoadExitTheta
    }
  }

  /**
   * 计算容器的窄边方向偏移角度
   */
  private fun calculateContainerNarrowSideOffset(containerType: SceneContainerType): Double {
    return when {
      // 长宽相等：正方体，不区别窄边或者宽边，使用 0 度偏移
      abs(containerType.outerLength - containerType.outerWidth) < 0.01 -> 0.0
      // 长 > 宽：窄边方向为垂直于长度的方向，需要旋转 90° 使窄边朝前
      containerType.outerLength > containerType.outerWidth -> PI / 2
      // 宽 > 长：窄边就是长度方向，不需要旋转，0°
      else -> 0.0
    }
  }

  /**
   * 获取缓存的容器类型信息
   */
  private fun getCachedContainerType(robotName: String): SceneContainerType? {
    val rr = sr.robots[robotName] ?: return null
    val main = rr.selfReport?.main ?: return null

    // 检查机器人是否有载货
    val loadRelations = main.loadRelations ?: emptyList()
    if (loadRelations.isEmpty()) return null

    val group = rr.mustGetGroup()
    if (!group.containerOversize) return null

    // 获取容器信息
    val collisionModel = RobotLoadCollisionService.buildCollisionModel(rr, main)
    if (!collisionModel.loaded) return null

    val containerTypeName = collisionModel.containerTypeName ?: return null

    return containerTypeCache.getOrPut(containerTypeName) {
      sr.containerTypes.values.find { it.name == containerTypeName } ?: return null
    }
  }

  /**
   * 寻找最佳换向点的内部实现
   */
  private fun findOptimalRotationPoint(robotName: String, fromPointName: String, toPointName: String): String? {
    val rr = sr.robots[robotName] ?: return null
    val areaMapCache = getAreaMapCache(rr) ?: return null

    // 首先尝试向前查找换向点（优先选择距离目标较近的点）
    val forwardRotationPoint = findRotationPointForward(rr, areaMapCache, fromPointName, toPointName)
    if (forwardRotationPoint != null) {
      logger.debug("find forward rotation point：${fromPointName}to${toPointName}，RotationPoint=$forwardRotationPoint")
      return forwardRotationPoint
    }

    // 如果向前找不到，向后查找
    val backwardRotationPoint = findRotationPointBackward(rr, areaMapCache, fromPointName, toPointName)
    if (backwardRotationPoint != null) {
      logger.debug("find backward rotation point：${fromPointName}to${toPointName}，RotationPoint=$backwardRotationPoint")
      return backwardRotationPoint
    }

    logger.debug("can not find rotation point：${fromPointName}to${toPointName}")
    return null
  }

  /**
   * 获取机器人对应的区域地图缓存
   */
  private fun getAreaMapCache(rr: RobotRuntime): AreaMapCache? {
    val stand = rr.selfReport?.stand ?: return null
    val areaId = stand.areaId
    val group = rr.mustGetGroup()
    return sr.mapCache.areaById[areaId]?.groupedMaps?.get(group.id)
  }

  /**
   * 检查当前状态是否需要在换向点进行容器旋转
   */
  fun shouldRotateAtRotationPoint(robotName: String, fromState: State, path: MapPath): Boolean {
    val rr = sr.robots[robotName] ?: return false
    val areaMapCache = getAreaMapCache(rr) ?: return false

    // 检查是否需要窄边通行
    if (!checkNarrowSideAheadConditions(robotName, path, fromState)) return false

    // 检查当前是否在换向点
    val currentPointName = fromState.toPosition.pointName ?: return false
    val pointRecord = areaMapCache.pointNameMap[currentPointName] ?: return false

    return pointRecord.point.containerRotateAllowed
  }

  /**
   * 向前查找换向点（优先选择距离目标较近的点）
   */
  private fun findRotationPointForward(
    rr: RobotRuntime,
    areaMapCache: AreaMapCache,
    fromPointName: String,
    toPointName: String
  ): String? {
    val shortestPath = DijkstraCache.getPathOrCompute(rr, areaMapCache.areaId, fromPointName, toPointName)

    if (shortestPath.isNullOrEmpty()) return null

    // 从起点向目标点方向查找换向点，优先选择距离目标较近的点
    for (pointName in shortestPath) {
      val pointRecord = areaMapCache.pointNameMap[pointName]
      if (pointRecord != null && pointRecord.point.containerRotateAllowed) {
        return pointName
      }
    }

    return null
  }

  /**
   * 向后查找换向点（从目标点向起点方向查找）
   */
  private fun findRotationPointBackward(
    rr: RobotRuntime,
    areaMapCache: AreaMapCache,
    fromPointName: String,
    toPointName: String
  ): String? {
    val shortestPath = DijkstraCache.getPathOrCompute(rr, areaMapCache.areaId, fromPointName, toPointName)

    if (shortestPath.isNullOrEmpty()) return null

    // 从目标点向起点方向查找换向点
    for (pointName in shortestPath.reversed()) {
      val pointRecord = areaMapCache.pointNameMap[pointName]
      if (pointRecord != null && pointRecord.point.containerRotateAllowed) {
        return pointName
      }
    }

    return null
  }

  /**
   * 计算从当前朝向到目标朝向的最小角度差
   */
  private fun calculateOptimalRotationAngle(currentTheta: Double, targetTheta: Double): Double {
    var angleDiff = (targetTheta - currentTheta).normalizeRadian()

    // 如果角度差超过半圆（π），选择反向旋转
    if (abs(angleDiff) > PI) {
      angleDiff = if (angleDiff > 0) {
        angleDiff - 2 * PI
      } else {
        angleDiff + 2 * PI
      }
    }

    return angleDiff
  }

  /**
   * 检查是否需要进行窄边通行处理的内部实现
   */
  private fun checkNarrowSideAheadConditions(robotName: String, path: MapPath, fromState: State): Boolean {
    // 1. 检查路径是否设置了窄边通行
    if (!path.containerShortSideAhead) return false

    // 2. 检查机器人是否存在
    val rr = sr.robots[robotName] ?: return false

    // 3. 检查机器人是否有自报信息
    val main = rr.selfReport?.main ?: return false

    // 4. 检查机器人是否有容器
    val loadRelations = main.loadRelations ?: emptyList()
    if (loadRelations.isEmpty()) return false

    // 5. 检查机器人组是否支持容器旋转（salverNotRotate为false表示支持旋转）
    val group = rr.mustGetGroup()
    if (group.salverNotRotate) return false

    // 6. 检查是否是超尺寸容器
    if (!group.containerOversize) return false

    // 7. 检查容器类型是否存在
    val containerType = getCachedContainerType(robotName) ?: return false

    // 8. 通过碰撞模型检查容器是否可独立旋转
    val collisionModel = RobotLoadCollisionService.buildCollisionModel(rr, main)
    if (!collisionModel.loaded || !collisionModel.loadRotatable) return false

    return true
  }

  /**
   * 货物朝向计算的内部实现，不涉及机器人朝向的计算
   */
  private fun calculateOptimalLoadThetas(
    robotName: String,
    originalLoadEnterTheta: Double,
    originalLoadExitTheta: Double,
    pathEnterTheta: Double, // 路径正向进入角度
    pathExitTheta: Double   // 路径正向退出角度
  ): Pair<Double, Double> {
    // 获取容器类型信息
    val containerType = getCachedContainerType(robotName)
      ?: return originalLoadEnterTheta to originalLoadExitTheta // 安全回退

    // 计算容器窄边偏移角度
    val narrowSideOffset = calculateContainerNarrowSideOffset(containerType)

    // 定义计算目标货物朝向的核心逻辑
    fun calculateOptimalLoadTheta(currentLoadTheta: Double, pathTheta: Double): Double {
      // 计算窄边通行所需的目标朝向：路径方向 + 窄边偏移
      val targetTheta = (pathTheta + narrowSideOffset).normalizeRadian()

      // 计算当前朝向到目标朝向的角度差
      val angleDiff = calculateOptimalRotationAngle(currentLoadTheta, targetTheta)

      // 如果角度差很小（小于容差），认为已经满足窄边要求，不需要调整
      if (abs(angleDiff) < angleTolerance) {
        return currentLoadTheta
      }

      // 否则返回目标朝向
      return targetTheta
    }

    // 分别计算进入和退出时的最优货物朝向
    val adjustedLoadEnterTheta = calculateOptimalLoadTheta(originalLoadEnterTheta, pathEnterTheta)
    val adjustedLoadExitTheta = calculateOptimalLoadTheta(originalLoadExitTheta, pathExitTheta)

    return adjustedLoadEnterTheta to adjustedLoadExitTheta
  }

  /**
   * 获取机器人与货物之间的固定偏移角度
   */
  fun getLoadToRobotOffset(robotName: String): Double {
    val rr = sr.robots[robotName] ?: return 0.0
    val main = rr.selfReport?.main ?: return 0.0

    try {
      val collisionModel = RobotLoadCollisionService.buildCollisionModel(rr, main)
      return if (collisionModel.loaded) {
        // 使用配置中的货物初始角度作为偏移角
        collisionModel.loadInitTheta
      } else {
        0.0
      }
    } catch (e: Exception) {
      logger.warn("get Load To Robot Offset failed: robotName=$robotName, error=${e.message}")
      return 0.0
    }
  }

  /**
   * 清理缓存
   */
  fun clearCache() {
    containerTypeCache.clear()
  }

  /**
   * 获取缓存统计信息
   */
  fun getCacheStats(): Map<String, Any> {
    return mapOf(
      "containerTypeCacheSize" to containerTypeCache.size,
      "containerTypeCacheKeys" to containerTypeCache.keys.toList()
    )
  }
}